#$binpath = “C:\Program Files\Microsoft SQL Server Compact Edition\v3.5\Desktop\”
$binpath = "C:\Program Files\Microsoft SQL Server Compact Edition\v4.0\Desktop\"
[Reflection.Assembly]::LoadFile("$binpath\System.Data.SqlServerCe.dll")


#-----------------------
function cleannames
{
param ($dirtynames)
$scrublist=$dirtynames -replace '\n',"`,"
$scrublist=$scrublist -replace " ","" 
$scrublist=$scrublist -replace "LogonName",""
$scrublist=$scrublist -replace "---------",""
$scrublist=$scrublist.ToString().Trim() -split ','
$scrublist=$scrublist.split('',[System.StringSplitOptions]::RemoveEmptyEntries)

return $scrublist
}


#-----------------------
function userenabled
{
param ($san)
#$san="mastevens"
$getad = (([adsisearcher]"(&(objectCategory=User)(samaccountname=$san))").findall())| select -ExpandProperty properties

$enabled=""

if ([string]$getad.useraccountcontrol -match "514") 
    {
    $enabled=$false
    }
else
    {
    $enabled=$true
    }

$itemHT=new-object psobject -Property @{
    samaccountname = [string]$getad.samaccountname
    enabled = $enabled
    }

return $itemHT 

##Computer properties
#$pc = 'computername'
#$getad = (([adsisearcher]"(&(objectCategory=Computer)(name=$pc))").findall()).properties
#$getad
}
#------------------------
$starttime=get-date
#$connString = "Data Source='C:\Users\<USER>\Desktop\APRDBBackup\testFiles\apr.sdf';" 
$connString = "Data Source='C:\Program Files\Netwrix Password Reset\apr.sdf';" 


$cn = new-object "System.Data.SqlServerCe.SqlCeConnection" $connString

# create the command 
$cmd = new-object "System.Data.SqlServerCe.SqlCeCommand" 
$cmd.CommandType = [System.Data.CommandType]"Text" 
$cmd.CommandText = "" 
$cmd.Connection = $cn

#get the data 
$dt = new-object "System.Data.DataTable"

$cn.Open() 


#populate usernames from ALL users
#--------
#$cmd.CommandText = "select * from Usr WHERE Usr.LogonName = `'$userlist[0]`'" 
$cmd.CommandText = "select LogonName from Usr"

Write-Progress -Activity "Gathering Database Items" -status "Getting Initial user list" -percentComplete 1

$rdr = $cmd.ExecuteReader()
$dt.Load($rdr) 

#$userlist = $dt -split '\n'
$userlist= $dt | Out-String

$userlist=CleanNames $userlist

$precount=$userlist.count
#$precount
$dt.Dispose()
$dt = new-object "System.Data.DataTable"

#populate usernames for people NOT ENROLLED
#----

$cmd.CommandText = "select LogonName from Usr Where Usr.LastEnrollTime IS NULL"

Write-Progress -Activity "Gathering Database Items" -status "Checking for unenrolled users" -percentComplete 50
$rdr = $cmd.ExecuteReader()
$dt.Load($rdr) 

$unenrolledlist= $dt | Out-String

$unenrolledlist=CleanNames $unenrolledlist
$unenrolledcount=$unenrolledlist.count
#$unenrolledcount
$dt.Dispose()
$dt = new-object "System.Data.DataTable"



#----
Write-Progress -Activity "Gathering Database Items" -status "Checking for unenrolled users" -percentComplete 100
#----------

#populate usernames of disabled users who can be removed
$disabledlist=@()
$i=0
foreach ($user in $userlist)
    {
    $i+=1
    Write-Progress -Activity "Looking for Disabled Users" -status "User Processed: $($user)" -percentComplete ($i / $userList.count*100)
    if (!(userenabled $user).enabled)
        {
        $disabledlist+=$user
        }
    }
$disabledlistcount=$disabledlist.count

#--------


#Combine lists into a Drop list
$RemoveUserlist=@()
$RemoveUserlist+=$disabledlist
$RemoveUserlist+=$unenrolledlist


# CLEAN USERS FROM DATABASE 
$i=0
foreach ($user in $RemoveUserlist)
    {
    $i+=1
    Write-Progress -Activity "Cleaning Database" -status "User Processed: $($user)" -percentComplete ($i / $RemoveUserlist.count*100)
    $cmd.CommandText = "DELETE FROM Usr WHERE Usr.LogonName like '$($user)%'"
    $rdr = $cmd.ExecuteReader()
    $dt.Load($rdr) 
    #$dt
    }

$tempsqldump=$dt
    

# ----shrink db
Write-Progress -Activity "Gathering Final Database Items" -status "Shrinking Database" -percentComplete 98
$engine = New-Object "System.Data.SqlServerCe.SqlCeEngine" $connString
$shrinkdata=$engine.Shrink()
$engine.Dispose()
 

#-----------get final account count
Write-Progress -Activity "Gathering Final Database Items" -status "Getting new user count" -percentComplete 99

#reinit
$dt.Dispose()
$dt = new-object "System.Data.DataTable"
$userlist=""

$cmd.CommandText = "select LogonName from Usr"

$rdr = $cmd.ExecuteReader()
$dt.Load($rdr) 

$userlist= $dt | Out-String
$userlist=CleanNames $userlist

Write-Progress -Activity "Gathering Final Database Items" -status "Getting new user count" -percentComplete 100

$finishedCount=$userlist.count


$stoptime=get-date
$totaltime= New-TimeSpan -Start $starttime -End $stoptime 


write-host "Starting Count: $($precount)"
write-host "disabledAcct count: $($disabledlistcount)"
write-host "unenrolled count: $($unenrolledcount)"
write-host "finished Count: $($finishedCount)"
write-host "TotalProcTime (minutes): $($totaltime.TotalMinutes)"

$cmd.Dispose()    
$cn.Close()
$cn.Dispose()

    


#$dt | Out-Default | Format-Table

#$dt |select LogonName


#For($i = 1; $i -le $colItems.count; $i++)

#{ Write-Progress -Activity "Gathering Services" -status "Found Service $i" `

#-percentComplete ($i / $colItems.count*100)}